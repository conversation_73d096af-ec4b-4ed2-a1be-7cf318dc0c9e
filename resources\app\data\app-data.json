{"rawMaterials": [{"id": 1, "name": "سكر أبيض", "quantity": 50, "unit": "كيلو", "price": 3.5, "minStock": 10, "maxStock": 100, "location": "م<PERSON>زن جاف", "supplier": "مو<PERSON><PERSON> السكر", "barcode": "1234567890123", "expiryDate": null, "category": "مواد أساسية"}, {"id": 2, "name": "برتقال طازج", "quantity": 100, "unit": "كيلو", "price": 4, "minStock": 20, "maxStock": 200, "location": "ثلاجة المواد", "supplier": "مزرعة الحمضيات", "barcode": "1234567890124", "expiryDate": "2024-12-31", "category": "فواكه طازجة"}, {"id": 3, "name": "ت<PERSON><PERSON><PERSON> أحمر", "quantity": 80, "unit": "كيلو", "price": 5, "minStock": 15, "maxStock": 150, "location": "ثلاجة المواد", "supplier": "مزرعة التفاح", "barcode": "1234567890125", "expiryDate": "2024-12-25", "category": "فواكه طازجة"}, {"id": 4, "name": "فراولة مجمدة", "quantity": 35, "unit": "كيلو", "price": 15, "minStock": 5, "maxStock": 50, "location": "فريزر", "supplier": "شركة المجمدات", "barcode": "1234567890126", "expiryDate": "2025-06-30", "category": "فواكه مجمدة", "supplierId": 1749990443576, "supplierName": "هو", "lastPurchaseDate": "2025-06-15T12:28:21.137Z", "lastUpdated": "2025-06-15T12:28:21.137Z"}, {"id": 5, "name": "حليب طازج", "quantity": 30, "unit": "لتر", "price": 5, "minStock": 10, "maxStock": 60, "location": "ثلاجة المواد", "supplier": "مصنع الألبان", "barcode": "1234567890127", "expiryDate": "2024-12-20", "category": "منتجا<PERSON> ألبان"}, {"id": 6, "name": "عسل طبيعي", "quantity": 10, "unit": "كيلو", "price": 25, "minStock": 3, "maxStock": 20, "location": "م<PERSON>زن جاف", "supplier": "م<PERSON><PERSON><PERSON> العسل", "barcode": "1234567890128", "expiryDate": null, "category": "مواد طبيعية"}, {"id": 7, "name": "ماء معدني", "quantity": 200, "unit": "لتر", "price": 0.5, "minStock": 50, "maxStock": 500, "location": "م<PERSON><PERSON>ن عام", "supplier": "شركة المياه", "barcode": "1234567890129", "expiryDate": null, "category": "مياه"}], "finishedProducts": [{"id": 1, "name": "عصير برتقال طبيعي", "quantity": 20, "sellPrice": 8, "cost": 3.2, "productionDate": "2025-06-15", "expiryDays": 3, "category": "عصائر طازجة", "barcode": "2234567890123", "batchNumber": "BATCH-001", "location": "ثلاجة العرض", "shelfLife": 3}, {"id": 2, "name": "عصير تفاح طازج", "quantity": 15, "sellPrice": 7, "cost": 2.8, "productionDate": "2025-06-15", "expiryDays": 3, "category": "عصائر طازجة", "barcode": "2234567890124", "batchNumber": "BATCH-002", "location": "ثلاجة العرض", "shelfLife": 3}, {"id": 3, "name": "سموثي فراولة", "quantity": 12, "sellPrice": 12, "cost": 5.5, "productionDate": "2025-06-15", "expiryDays": 2, "category": "سموثي", "barcode": "2234567890125", "batchNumber": "BATCH-003", "location": "ثلاجة العرض", "shelfLife": 2}], "customers": [{"id": 1, "name": "<PERSON><PERSON><PERSON><PERSON> محمد", "phone": "0599123456", "email": "<EMAIL>", "address": "رام الله - المنارة", "registrationDate": "2025-06-20", "totalPurchases": 0, "totalAmount": 0, "lastPurchaseDate": null, "loyaltyPoints": 0, "creditLimit": 1000, "currentDebt": 0, "customerType": "عادي", "notes": "", "isActive": true, "discountPercentage": 0, "preferredPaymentMethod": "نقدي", "birthDate": null, "gender": "ذكر", "occupation": "", "referredBy": null, "socialMedia": {"facebook": "", "instagram": "", "whatsapp": "0599123456"}, "purchaseHistory": [], "paymentHistory": [], "lastUpdated": "2025-06-20T00:00:00.000Z"}], "sales": [], "costs": [{"type": "مشتريات", "amount": 150, "description": "مشترى من هو: مواد خام: فراولة مجمدة (10 كيلو)", "supplierId": 1749990443576, "id": 1749990501138, "date": "2025-06-15T12:28:21.138Z"}], "purchases": [{"type": "مو<PERSON> خام", "supplierId": 1749990443576, "supplierName": "هو", "invoiceNumber": "PUR-1749990501138", "description": "مواد خام: فراولة مجمدة (10 كيلو)", "totalCost": 150, "paymentMethod": "نقدي", "date": "2025-06-15T12:28:21.138Z", "addedDate": "2025-06-15T12:28:21.138Z", "items": [{"materialId": 4, "name": "فراولة مجمدة", "quantity": 10, "unit": "كيلو", "price": 15, "totalCost": 150, "isNew": false}], "id": 1749990501138}], "settings": {"shopName": "محل العصائر والحلويات الفلسطيني", "shopAddress": "فلسطين - را<PERSON> الله", "shopPhone": "0569329925", "shopEmail": "<EMAIL>", "currency": "شيكل", "currencySymbol": "₪", "country": "فلسطين", "taxRate": 0, "soundEnabled": true, "autoBackup": true, "lowStockAlert": true, "expiryAlert": true, "profitMarginTarget": 50, "backupSettings": {"autoBackup": true, "backupFrequency": "weekly", "lastBackup": null}, "lastUpdated": "2025-06-15T12:21:06.010Z", "alertSettings": {"lowStockAlert": true, "expiryAlert": true, "alertDays": 3, "profitMarginTarget": 50, "allowNegativeStock": false}, "indirectCostSettings": {"indirectCostsEnabled": true, "costDistributionMethod": "units", "additionalCostPercentage": 15, "includeRent": true, "includeElectricity": true, "includeWater": true, "includeSalaries": true, "includeMaintenance": true, "includeOther": true}}, "customRecipes": [], "recurringCosts": [], "salesDrafts": [], "lastSaved": "2025-06-18T10:19:04.777Z", "systemVersion": "2.3.0", "backupHistory": [], "importHistory": [], "autoBackups": [], "systemLogs": [{"timestamp": "2025-06-19T00:00:00.000Z", "event": "SYSTEM_UPGRADE", "description": "تم ترقية النظام إلى الإصدار 2.3.0 مع نظام النسخ الاحتياطي والاستيراد المحسن", "version": "2.3.0", "developer": "Fares Nawaf - 0569329925"}]}